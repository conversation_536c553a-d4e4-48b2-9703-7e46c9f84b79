#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试SR1041D的出现次数
"""

import pandas as pd
import csv
import sys

def count_device_occurrences(file_path, target_device="SR1041D"):
    """统计设备名称的出现次数"""
    
    print(f"=== 统计 {target_device} 的出现次数 ===")
    
    # 设置CSV字段大小限制
    maxInt = sys.maxsize
    while True:
        try:
            csv.field_size_limit(maxInt)
            break
        except OverflowError:
            maxInt = int(maxInt/10)
    
    total_count = 0
    chunk_count = 0
    
    try:
        # 使用最宽松的CSV读取参数
        csv_params = {
            'engine': 'python',
            'sep': ',',
            'quoting': csv.QUOTE_NONE,
            'doublequote': False,
            'escapechar': None,
            'on_bad_lines': 'skip',
            'encoding_errors': 'ignore',
            'chunksize': 10000
        }
        
        for chunk in pd.read_csv(file_path, **csv_params):
            chunk_count += 1
            
            # 清理数据
            chunk = chunk.dropna(subset=['deviceName'])
            chunk['deviceName'] = chunk['deviceName'].astype(str)
            chunk = chunk[chunk['deviceName'].str.strip() != '']
            chunk = chunk[chunk['deviceName'].str.len() <= 500]
            
            # 统计目标设备的出现次数
            target_count = (chunk['deviceName'] == target_device).sum()
            total_count += target_count
            
            if target_count > 0:
                print(f"块 {chunk_count}: 找到 {target_count} 个 {target_device}")
            
            # 每处理100个块显示进度
            if chunk_count % 100 == 0:
                print(f"已处理 {chunk_count} 个数据块，累计找到 {total_count} 个 {target_device}")
    
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        return 0
    
    print(f"\n=== 统计结果 ===")
    print(f"{target_device} 总出现次数: {total_count}")
    print(f"阈值设置: 50")
    
    if total_count >= 50:
        print(f"✅ {target_device} 出现次数 >= 50，应该出现在输出中")
    else:
        print(f"❌ {target_device} 出现次数 < 50，不会出现在输出中")
    
    return total_count

def main():
    """主函数"""
    
    file_path = "/home/<USER>/HBHGU20250818.csv"
    
    # 统计SR1041D
    count_device_occurrences(file_path, "SR1041D")
    
    print(f"\n=== 说明 ===")
    print("修改后的脚本逻辑:")
    print("1. 不再按deviceId+deviceName去重")
    print("2. 直接统计deviceName的出现次数")
    print("3. 出现次数>=50的设备名称会被保留")
    print("4. SR1041D如果出现50次，就应该在输出中")

if __name__ == "__main__":
    main()
