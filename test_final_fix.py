#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终修复版本
"""

from device_name_cleaner import DeviceNameCleaner
import csv
import sys
import os

def test_complete_process():
    """测试完整的处理流程"""
    
    print("=== 测试完整处理流程 ===")
    
    # 设置CSV字段大小限制
    maxInt = sys.maxsize
    while True:
        try:
            csv.field_size_limit(maxInt)
            break
        except OverflowError:
            maxInt = int(maxInt/10)
    
    # 配置参数
    input_file = "/home/<USER>/HBHGU20250818.csv"
    output_file = "测试_最终修复版.csv"
    chunk_size = 2000  # 使用很小的块进行快速测试
    user_threshold = 50
    element_threshold = 50
    
    if not os.path.exists(input_file):
        print(f"文件不存在: {input_file}")
        return False
    
    print(f"配置参数:")
    print(f"  输入文件: {input_file}")
    print(f"  输出文件: {output_file}")
    print(f"  块大小: {chunk_size}")
    print(f"  出现次数阈值: {user_threshold}")
    print(f"  元素频次阈值: {element_threshold}")
    
    # 创建清洗器实例
    cleaner = DeviceNameCleaner(
        input_file=input_file,
        output_file=output_file,
        chunk_size=chunk_size,
        user_threshold=user_threshold,
        element_threshold=element_threshold
    )
    
    try:
        print(f"\n开始测试...")
        
        # 测试第一阶段
        print("第一阶段：统计设备名称出现次数")
        cleaner.step1_count_device_users()
        
        device_count = len(cleaner.device_user_counts)
        print(f"✅ 第一阶段成功，发现 {device_count:,} 个不同设备名称")
        
        # 检查SR1041D
        sr1041d_count = cleaner.device_user_counts.get('SR1041D', 0)
        print(f"SR1041D 出现次数: {sr1041d_count}")
        
        # 测试第二阶段
        print(f"\n第二阶段：分析设备名称分布")
        high_devices, low_devices = cleaner.step2_analyze_device_names()
        print(f"✅ 第二阶段成功")
        
        # 测试第三阶段
        print(f"\n第三阶段：统计元素频次")
        element_counts = cleaner.step3_count_elements()
        print(f"✅ 第三阶段成功，发现 {len(element_counts):,} 个元素")
        
        # 测试第四阶段
        print(f"\n第四阶段：生成清洗表")
        result_df = cleaner.step4_generate_clean_table()
        
        if result_df is not None and len(result_df) > 0:
            print(f"✅ 第四阶段成功，生成 {len(result_df):,} 条清洗记录")
            
            # 检查列名
            print(f"输出列名: {list(result_df.columns)}")
            
            # 检查是否有"出现次数"列
            if '出现次数' in result_df.columns:
                print(f"✅ '出现次数'列存在")
                
                # 显示统计信息
                type_counts = result_df['处理类型'].value_counts()
                for proc_type, count in type_counts.items():
                    print(f"  {proc_type}: {count:,} 条")
                
                # 检查SR1041D是否在结果中
                sr1041d_in_result = result_df[result_df['清洗前名称'] == 'SR1041D']
                if not sr1041d_in_result.empty:
                    print(f"✅ SR1041D 在结果中:")
                    for _, row in sr1041d_in_result.iterrows():
                        print(f"    {row['清洗前名称']} -> {row['清洗后名称']} (出现次数: {row['出现次数']})")
                else:
                    print(f"❌ SR1041D 不在结果中")
                
            else:
                print(f"❌ '出现次数'列不存在，可能还有问题")
                return False
        else:
            print(f"❌ 第四阶段失败，未生成结果")
            return False
        
        print(f"\n🎉 所有阶段测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    
    success = test_complete_process()
    
    if success:
        print(f"\n✅ 脚本完全修复成功！")
        print("现在可以运行完整的清洗流程:")
        print("python device_name_cleaner.py")
        print("\n或者从断点恢复:")
        print("cleaner.resume_from_step(2)")
    else:
        print(f"\n❌ 脚本仍有问题")

if __name__ == "__main__":
    main()
