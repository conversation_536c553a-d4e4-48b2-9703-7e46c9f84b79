#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从断点恢复处理
"""

from device_name_cleaner import DeviceNameCleaner
import csv
import sys
import os

def resume_processing():
    """从断点恢复处理"""
    
    print("=== 从断点恢复处理 ===")
    
    # 设置CSV字段大小限制
    maxInt = sys.maxsize
    while True:
        try:
            csv.field_size_limit(maxInt)
            break
        except OverflowError:
            maxInt = int(maxInt/10)
    
    # 配置参数（与原脚本保持一致）
    input_file = "/home/<USER>/HBHGU20250818.csv"
    output_file = "设备名称清洗表.csv"
    chunk_size = 10000
    user_threshold = 50
    element_threshold = 50
    
    print(f"配置参数:")
    print(f"  输入文件: {input_file}")
    print(f"  输出文件: {output_file}")
    print(f"  出现次数阈值: {user_threshold}")
    print(f"  元素频次阈值: {element_threshold}")
    
    # 创建清洗器实例
    cleaner = DeviceNameCleaner(
        input_file=input_file,
        output_file=output_file,
        chunk_size=chunk_size,
        user_threshold=user_threshold,
        element_threshold=element_threshold
    )
    
    # 检查是否有中间结果
    temp_file = "temp_processing/device_counts.pkl"
    if os.path.exists(temp_file):
        print(f"✅ 发现中间结果文件: {temp_file}")
        
        try:
            # 加载中间结果
            cleaner._load_device_counts()
            device_count = len(cleaner.device_user_counts)
            print(f"已加载 {device_count:,} 个设备名称的统计数据")
            
            # 检查SR1041D
            sr1041d_count = cleaner.device_user_counts.get('SR1041D', 0)
            print(f"SR1041D 出现次数: {sr1041d_count}")
            
            if sr1041d_count >= user_threshold:
                print(f"✅ SR1041D 出现次数 >= {user_threshold}，应该会在输出中")
            else:
                print(f"❌ SR1041D 出现次数 < {user_threshold}，不会在输出中")
            
            # 从第2阶段开始恢复
            print(f"\n从第2阶段开始恢复处理...")
            result_df = cleaner.resume_from_step(2)
            
            if result_df is not None and len(result_df) > 0:
                print(f"🎉 恢复处理成功！")
                print(f"生成清洗记录: {len(result_df):,} 条")
                
                # 显示统计信息
                type_counts = result_df['处理类型'].value_counts()
                for proc_type, count in type_counts.items():
                    print(f"  {proc_type}: {count:,} 条")
                
                # 检查SR1041D
                sr1041d_in_result = result_df[result_df['清洗前名称'] == 'SR1041D']
                if not sr1041d_in_result.empty:
                    print(f"✅ SR1041D 在最终结果中:")
                    for _, row in sr1041d_in_result.iterrows():
                        print(f"    {row['清洗前名称']} -> {row['清洗后名称']} (出现次数: {row['出现次数']})")
                else:
                    print(f"❌ SR1041D 不在最终结果中")
                
                print(f"\n输出文件: {output_file}")
                
            else:
                print(f"❌ 恢复处理失败")
                
        except Exception as e:
            print(f"❌ 恢复处理失败: {e}")
            import traceback
            traceback.print_exc()
    
    else:
        print(f"❌ 未找到中间结果文件: {temp_file}")
        print("需要重新运行完整流程:")
        print("python device_name_cleaner.py")

def main():
    """主函数"""
    
    resume_processing()

if __name__ == "__main__":
    main()
