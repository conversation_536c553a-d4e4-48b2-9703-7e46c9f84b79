#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的脚本
"""

from device_name_cleaner import DeviceNameCleaner
import csv
import sys
import os

def test_fixed_script():
    """测试修复后的脚本"""
    
    print("=== 测试修复后的脚本 ===")
    
    # 设置CSV字段大小限制
    maxInt = sys.maxsize
    while True:
        try:
            csv.field_size_limit(maxInt)
            break
        except OverflowError:
            maxInt = int(maxInt/10)
    
    # 配置参数
    input_file = "/home/<USER>/HBHGU20250818.csv"
    output_file = "测试_修复版.csv"
    chunk_size = 5000  # 使用小块进行测试
    user_threshold = 50
    element_threshold = 50
    
    if not os.path.exists(input_file):
        print(f"文件不存在: {input_file}")
        return
    
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print(f"块大小: {chunk_size}")
    print(f"出现次数阈值: {user_threshold}")
    print(f"元素频次阈值: {element_threshold}")
    
    # 创建清洗器实例
    cleaner = DeviceNameCleaner(
        input_file=input_file,
        output_file=output_file,
        chunk_size=chunk_size,
        user_threshold=user_threshold,
        element_threshold=element_threshold
    )
    
    try:
        print("\n开始测试第一阶段...")
        
        # 只测试第一阶段（统计设备名称出现次数）
        cleaner.step1_count_device_users()
        
        print(f"\n第一阶段测试成功！")
        print(f"发现 {len(cleaner.device_user_counts):,} 个不同的设备名称")
        
        # 检查SR1041D
        sr1041d_count = cleaner.device_user_counts.get('SR1041D', 0)
        print(f"SR1041D 出现次数: {sr1041d_count}")
        
        if sr1041d_count >= user_threshold:
            print(f"✅ SR1041D 出现次数 >= {user_threshold}，应该会出现在输出中")
        else:
            print(f"❌ SR1041D 出现次数 < {user_threshold}，不会出现在输出中")
        
        # 显示出现次数最多的前10个设备
        print(f"\n出现次数最多的前10个设备:")
        for device, count in cleaner.device_user_counts.most_common(10):
            print(f"  {device}: {count} 次")
        
        # 显示出现次数>=阈值的设备数量
        high_freq_count = sum(1 for count in cleaner.device_user_counts.values() if count >= user_threshold)
        print(f"\n出现次数>={user_threshold}的设备数量: {high_freq_count:,}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    
    success = test_fixed_script()
    
    if success:
        print(f"\n✅ 脚本修复成功！")
        print("现在可以运行完整的清洗流程:")
        print("python device_name_cleaner.py")
    else:
        print(f"\n❌ 脚本仍有问题，需要进一步修复")

if __name__ == "__main__":
    main()
