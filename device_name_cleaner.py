#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备名称清洗脚本 - 处理上亿条数据的分块处理方案
功能：
1. 按deviceId+deviceName去重
2. 统计deviceName用户数
3. 对用户数<10且含"-"的设备名称进行元素频次分析
4. 生成清洗后的设备名称表
"""

import pandas as pd
import pickle
import os
from collections import Counter, defaultdict
import re
from tqdm import tqdm
import gc
import time

class DeviceNameCleaner:
    def __init__(self, input_file, output_file, chunk_size=100000, user_threshold=10, element_threshold=10):
        """
        初始化设备名称清洗器

        Args:
            input_file: 输入CSV文件路径
            output_file: 输出清洗表CSV文件路径
            chunk_size: 每次处理的数据块大小
            user_threshold: 用户数阈值，>=此值的设备名称直接保留（默认10）
            element_threshold: 元素频次阈值，>=此值的元素用于重组设备名称（默认10）
        """
        self.input_file = input_file
        self.output_file = output_file
        self.chunk_size = chunk_size
        self.user_threshold = user_threshold
        self.element_threshold = element_threshold
        
        # 中间文件路径
        self.temp_dir = "temp_processing"
        self.device_count_file = os.path.join(self.temp_dir, "device_counts.pkl")
        self.element_count_file = os.path.join(self.temp_dir, "element_counts.pkl")
        
        # 创建临时目录
        os.makedirs(self.temp_dir, exist_ok=True)
        
        # 统计数据
        self.device_user_counts = Counter()  # deviceName -> 用户数
        self.element_counts = Counter()      # 分隔元素 -> 出现次数
        
        print(f"初始化完成")
        print(f"块大小: {chunk_size:,}")
        print(f"用户数阈值: {user_threshold} (>=此值直接保留)")
        print(f"元素频次阈值: {element_threshold} (>=此值用于重组)")

    def step1_count_device_users(self):
        """
        第一阶段：分块读取数据，统计每个deviceName的用户数
        """
        print("\n=== 第一阶段：统计设备名称用户数 ===")
        
        total_rows = 0
        chunk_count = 0
        
        try:
            # 增加CSV字段大小限制
            import csv
            csv.field_size_limit(1000000)  # 设置为1MB，足够大

            # 专门针对引号问题的pandas解决方案
            print("尝试pandas引号问题解决方案...")

            # 最激进的pandas参数组合
            csv_params = {
                'chunksize': self.chunk_size,
                'engine': 'python',           # 使用Python引擎
                'sep': ',',                   # 明确指定分隔符
                'quoting': csv.QUOTE_NONE,    # 完全忽略引号
                'doublequote': False,         # 不处理双引号转义
                'escapechar': None,           # 不使用转义字符
                'skipinitialspace': True,     # 跳过字段前空格
                'skiprows': None,             # 不跳过行
                'skip_blank_lines': True,     # 跳过空行
                'encoding_errors': 'ignore',  # 忽略编码错误
            }

            # 尝试添加错误跳过参数
            try:
                # 新版pandas
                csv_params['on_bad_lines'] = 'skip'
                csv_reader = pd.read_csv(self.input_file, **csv_params)
                print("✅ 使用新版pandas参数成功")
            except (TypeError, ValueError) as e:
                print(f"新版参数失败: {e}")
                try:
                    # 旧版pandas
                    csv_params.pop('on_bad_lines', None)
                    csv_params['error_bad_lines'] = False
                    csv_params['warn_bad_lines'] = False
                    csv_reader = pd.read_csv(self.input_file, **csv_params)
                    print("✅ 使用旧版pandas参数成功")
                except Exception as e2:
                    print(f"旧版参数也失败: {e2}")
                    # 最后尝试：最简参数
                    simple_params = {
                        'chunksize': self.chunk_size,
                        'engine': 'python',
                        'quoting': csv.QUOTE_NONE,
                        'on_bad_lines': 'skip' if 'on_bad_lines' in csv_params else None
                    }
                    simple_params = {k: v for k, v in simple_params.items() if v is not None}
                    csv_reader = pd.read_csv(self.input_file, **simple_params)
                    print("✅ 使用最简参数成功")

            for chunk in tqdm(csv_reader, desc="处理数据块"):
                chunk_count += 1

                try:
                    # 清理数据：移除空值
                    chunk = chunk.dropna(subset=['hguDeviceId', 'deviceName'])

                    # 过滤空字符串
                    chunk = chunk[chunk['deviceName'].str.strip() != '']

                    # 过滤过长的字段（可能是数据错误）
                    chunk = chunk[chunk['deviceName'].str.len() <= 500]  # 限制设备名称长度
                    if 'hguDeviceId' in chunk.columns:
                        chunk = chunk[chunk['hguDeviceId'].str.len() <= 100]  # 限制设备ID长度

                    # 按deviceId+deviceName去重（每个块内去重）
                    unique_combinations = chunk.drop_duplicates(subset=['deviceName'])

                    # 统计每个deviceName的用户数（在当前块中）
                    device_counts_in_chunk = unique_combinations['deviceName'].value_counts()

                    # 累加到总计数器
                    for device_name, count in device_counts_in_chunk.items():
                        self.device_user_counts[device_name] += count

                    total_rows += len(chunk)

                except Exception as chunk_error:
                    print(f"跳过数据块 {chunk_count}，原因: {chunk_error}")
                    continue
                
                # 每处理50个块保存一次中间结果（针对过亿行数据）
                if chunk_count % 50 == 0:
                    self._save_device_counts()
                    print(f"已处理 {chunk_count} 个数据块，累计 {total_rows:,} 行")
                    print(f"当前已发现 {len(self.device_user_counts):,} 个不同设备名称")

                    # 显示内存使用情况
                    try:
                        import psutil
                        memory = psutil.virtual_memory()
                        print(f"内存使用: {memory.percent:.1f}% (可用: {memory.available/1024/1024/1024:.1f}GB)")
                    except:
                        pass

                # 强制垃圾回收和内存清理
                del chunk, unique_combinations, device_counts_in_chunk
                gc.collect()

                # 每1000个块进行深度内存清理
                if chunk_count % 1000 == 0:
                    print(f"执行深度内存清理...")
                    gc.collect()
                    gc.collect()  # 执行两次确保彻底清理
                
        except Exception as e:
            print(f"处理过程中出现错误: {e}")
            # 保存当前进度
            self._save_device_counts()
            raise
        
        # 保存最终结果
        self._save_device_counts()
        
        print(f"第一阶段完成！")
        print(f"总共处理了 {chunk_count} 个数据块，{total_rows:,} 行数据")
        print(f"发现 {len(self.device_user_counts):,} 个不同的设备名称")
        
    def _save_device_counts(self):
        """保存设备用户数统计到文件"""
        with open(self.device_count_file, 'wb') as f:
            pickle.dump(dict(self.device_user_counts), f)
    
    def _load_device_counts(self):
        """从文件加载设备用户数统计"""
        if os.path.exists(self.device_count_file):
            with open(self.device_count_file, 'rb') as f:
                counts = pickle.load(f)
                self.device_user_counts = Counter(counts)
                print(f"加载了 {len(self.device_user_counts):,} 个设备名称的统计数据")
        else:
            print("未找到设备统计文件，需要先运行第一阶段")

    def step2_analyze_device_names(self):
        """
        第二阶段：分析设备名称，分离用户数>=10和<10的记录
        """
        print("\n=== 第二阶段：分析设备名称分布 ===")

        # 确保已加载设备统计数据
        if not self.device_user_counts:
            self._load_device_counts()

        # 分类设备名称
        high_count_devices = {}  # 用户数>=10的设备
        low_count_devices = {}   # 用户数<10的设备
        low_count_with_dash = {} # 用户数<10且含"-"的设备

        for device_name, user_count in self.device_user_counts.items():
            if user_count >= self.user_threshold:
                high_count_devices[device_name] = user_count
            else:
                low_count_devices[device_name] = user_count
                # 检查是否包含"-"
                if '-' in device_name:
                    low_count_with_dash[device_name] = user_count

        print(f"用户数>={self.user_threshold}的设备名称: {len(high_count_devices):,} 个")
        print(f"用户数<{self.user_threshold}的设备名称: {len(low_count_devices):,} 个")
        print(f"用户数<{self.user_threshold}且含'-'的设备名称: {len(low_count_with_dash):,} 个")

        # 保存分类结果
        self.high_count_devices = high_count_devices
        self.low_count_with_dash = low_count_with_dash

        return high_count_devices, low_count_with_dash

    def step3_count_elements(self):
        """
        第三阶段：统计用户数<10且含"-"的设备名称中各元素的出现频次
        """
        print("\n=== 第三阶段：统计分隔元素频次 ===")

        if not hasattr(self, 'low_count_with_dash'):
            print("需要先运行第二阶段")
            return

        print(f"开始分析 {len(self.low_count_with_dash):,} 个含'-'的设备名称...")

        # 统计所有分隔元素的出现次数
        for device_name, user_count in tqdm(self.low_count_with_dash.items(),
                                          desc="分析设备名称元素"):
            # 按"-"分隔设备名称
            elements = device_name.split('-')

            # 统计每个元素的出现次数（按该设备名称的用户数加权）
            for element in elements:
                element = element.strip()  # 去除空格
                if element:  # 忽略空元素
                    self.element_counts[element] += user_count

        # 保存元素统计结果
        self._save_element_counts()

        print(f"发现 {len(self.element_counts):,} 个不同的元素")
        print(f"出现频次>={self.element_threshold}的元素: {sum(1 for count in self.element_counts.values() if count >= self.element_threshold):,} 个")

        return self.element_counts

    def _save_element_counts(self):
        """保存元素频次统计到文件"""
        with open(self.element_count_file, 'wb') as f:
            pickle.dump(dict(self.element_counts), f)

    def _load_element_counts(self):
        """从文件加载元素频次统计"""
        if os.path.exists(self.element_count_file):
            with open(self.element_count_file, 'rb') as f:
                counts = pickle.load(f)
                self.element_counts = Counter(counts)
                print(f"加载了 {len(self.element_counts):,} 个元素的频次数据")
        else:
            print("未找到元素统计文件，需要先运行第三阶段")

    def step4_generate_clean_table(self):
        """
        第四阶段：生成清洗表
        """
        print("\n=== 第四阶段：生成设备名称清洗表 ===")

        # 确保数据已加载
        if not self.device_user_counts:
            self._load_device_counts()
        if not self.element_counts:
            self._load_element_counts()

        # 准备清洗表数据
        clean_records = []

        # 1. 处理用户数>=阈值的设备名称（直接保留）
        print(f"处理用户数>={self.user_threshold}的设备名称...")
        for device_name, user_count in tqdm(self.device_user_counts.items(),
                                          desc="处理高频设备"):
            if user_count >= self.user_threshold:
                clean_records.append({
                    '清洗前名称': device_name,
                    '清洗后名称': device_name,
                    '用户数': user_count,
                    '处理类型': '高频保留'
                })

        # 2. 处理用户数<阈值且含"-"的设备名称
        print(f"处理用户数<{self.user_threshold}且含'-'的设备名称...")
        processed_low_count = 0

        for device_name, user_count in tqdm(self.device_user_counts.items(),
                                          desc="处理低频设备"):
            if user_count < self.user_threshold and '-' in device_name:
                # 分隔设备名称
                elements = device_name.split('-')

                # 筛选出现频次>=阈值的元素
                valid_elements = []
                for element in elements:
                    element = element.strip()
                    if element and self.element_counts.get(element, 0) >= self.element_threshold:
                        valid_elements.append(element)

                # 如果有有效元素，重新组合
                if valid_elements:
                    cleaned_name = '-'.join(valid_elements)
                    clean_records.append({
                        '清洗前名称': device_name,
                        '清洗后名称': cleaned_name,
                        '用户数': user_count,
                        '处理类型': '低频重组'
                    })
                    processed_low_count += 1

        print(f"成功处理了 {processed_low_count:,} 个低频含'-'的设备名称")

        # 3. 生成清洗表DataFrame
        df_clean = pd.DataFrame(clean_records)

        # 4. 保存到CSV文件
        df_clean.to_csv(self.output_file, index=False, encoding='utf-8-sig')

        print(f"清洗表已保存到: {self.output_file}")
        print(f"总共生成 {len(clean_records):,} 条清洗记录")

        # 5. 生成统计报告
        self._generate_report(df_clean)

        return df_clean

    def _generate_report(self, df_clean):
        """生成处理报告"""
        print("\n=== 处理报告 ===")

        # 按处理类型统计
        type_counts = df_clean['处理类型'].value_counts()
        print("按处理类型统计:")
        for proc_type, count in type_counts.items():
            print(f"  {proc_type}: {count:,} 条")

        # 用户数分布统计
        total_users_high = df_clean[df_clean['处理类型'] == '高频保留']['用户数'].sum()
        total_users_low = df_clean[df_clean['处理类型'] == '低频重组']['用户数'].sum()

        print(f"\n用户数分布:")
        print(f"  高频设备覆盖用户数: {total_users_high:,}")
        print(f"  低频重组覆盖用户数: {total_users_low:,}")
        print(f"  总覆盖用户数: {total_users_high + total_users_low:,}")

        # 保存详细报告
        report_file = self.output_file.replace('.csv', '_report.txt')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("设备名称清洗处理报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"输入文件: {self.input_file}\n")
            f.write(f"输出文件: {self.output_file}\n")
            f.write(f"处理时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write("处理统计:\n")
            for proc_type, count in type_counts.items():
                f.write(f"  {proc_type}: {count:,} 条\n")

            f.write(f"\n用户覆盖统计:\n")
            f.write(f"  高频设备覆盖用户数: {total_users_high:,}\n")
            f.write(f"  低频重组覆盖用户数: {total_users_low:,}\n")
            f.write(f"  总覆盖用户数: {total_users_high + total_users_low:,}\n")

        print(f"详细报告已保存到: {report_file}")

    def run_full_process(self):
        """运行完整的清洗流程"""
        print("开始设备名称清洗流程...")
        start_time = time.time()

        try:
            # 第一阶段：统计设备用户数
            self.step1_count_device_users()

            # 第二阶段：分析设备名称分布
            self.step2_analyze_device_names()

            # 第三阶段：统计元素频次
            self.step3_count_elements()

            # 第四阶段：生成清洗表
            df_result = self.step4_generate_clean_table()

            end_time = time.time()
            total_time = end_time - start_time

            print(f"\n=== 处理完成 ===")
            print(f"总耗时: {total_time/3600:.2f} 小时")
            print(f"清洗表文件: {self.output_file}")

            return df_result

        except Exception as e:
            print(f"处理过程中出现错误: {e}")
            print("中间结果已保存，可以从断点继续处理")
            raise

    def resume_from_step(self, step_number):
        """从指定步骤恢复处理"""
        print(f"从第{step_number}阶段恢复处理...")

        if step_number <= 1:
            return self.run_full_process()

        # 加载之前的结果
        self._load_device_counts()

        if step_number <= 2:
            self.step2_analyze_device_names()
            self.step3_count_elements()
            return self.step4_generate_clean_table()

        if step_number <= 3:
            self.step2_analyze_device_names()
            self.step3_count_elements()
            return self.step4_generate_clean_table()

        if step_number <= 4:
            self._load_element_counts()
            self.step2_analyze_device_names()
            return self.step4_generate_clean_table()

    def cleanup_temp_files(self):
        """清理临时文件"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
            print("临时文件已清理")


def main():
    """主函数 - 使用示例"""

    # 配置参数
    input_file = "device_name.csv"  # 输入文件路径
    output_file = "设备名称清洗表.csv"  # 输出文件路径
    chunk_size = 10000  # 每次处理1万行
    user_threshold = 50    # 用户数阈值
    element_threshold = 50 # 元素频次阈值

    # nohup  python device_name_cleaner.py  > device_name_cleaner.log 2>&1 &

    # 创建清洗器实例
    cleaner = DeviceNameCleaner(
        input_file=input_file,
        output_file=output_file,
        chunk_size=chunk_size,
        user_threshold=user_threshold,
        element_threshold=element_threshold
    )

    try:
        # 运行完整流程
        result_df = cleaner.run_full_process()

        # 显示前几行结果
        print("\n清洗结果预览:")
        print(result_df.head(10))

    except KeyboardInterrupt:
        print("\n用户中断处理")
        print("可以使用 resume_from_step() 方法从断点继续")
    except Exception as e:
        print(f"处理失败: {e}")
        print("可以检查临时文件并从适当步骤恢复")

    # 可选：清理临时文件（注释掉以保留中间结果）
    # cleaner.cleanup_temp_files()


if __name__ == "__main__":
    main()
