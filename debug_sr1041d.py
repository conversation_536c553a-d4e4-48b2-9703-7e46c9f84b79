#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试SR1041D设备名称的处理情况
"""

import pandas as pd
import csv
import sys
from collections import Counter

def debug_device_name(file_path, target_device="SR1041D"):
    """调试特定设备名称的处理情况"""
    
    print(f"=== 调试设备名称: {target_device} ===")
    
    # 设置CSV字段大小限制
    maxInt = sys.maxsize
    while True:
        try:
            csv.field_size_limit(maxInt)
            break
        except OverflowError:
            maxInt = int(maxInt/10)
    
    # 统计原始出现次数
    total_occurrences = 0
    unique_combinations = set()
    device_id_list = []
    
    print("正在分析原始数据...")
    
    try:
        # 使用最宽松的CSV读取参数
        csv_params = {
            'engine': 'python',
            'sep': ',',
            'quoting': csv.QUOTE_NONE,
            'doublequote': False,
            'escapechar': None,
            'on_bad_lines': 'skip',
            'encoding_errors': 'ignore',
            'chunksize': 10000
        }
        
        chunk_count = 0
        for chunk in pd.read_csv(file_path, **csv_params):
            chunk_count += 1
            
            # 清理数据
            chunk = chunk.dropna(subset=['hguDeviceId', 'deviceName'])
            chunk['hguDeviceId'] = chunk['hguDeviceId'].astype(str)
            chunk['deviceName'] = chunk['deviceName'].astype(str)
            
            # 过滤目标设备名称
            target_rows = chunk[chunk['deviceName'] == target_device]
            
            if len(target_rows) > 0:
                print(f"在第 {chunk_count} 个数据块中找到 {len(target_rows)} 条 {target_device} 记录")
                
                for _, row in target_rows.iterrows():
                    device_id = row['hguDeviceId']
                    device_name = row['deviceName']
                    
                    total_occurrences += 1
                    unique_combinations.add((device_id, device_name))
                    device_id_list.append(device_id)
                    
                    # 显示前几条记录
                    if total_occurrences <= 10:
                        print(f"  记录 {total_occurrences}: deviceId={device_id}, deviceName={device_name}")
            
            # 每处理100个块显示进度
            if chunk_count % 100 == 0:
                print(f"已处理 {chunk_count} 个数据块...")
    
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        return
    
    # 统计结果
    unique_device_ids = len(set(device_id_list))
    unique_combinations_count = len(unique_combinations)
    
    print(f"\n=== {target_device} 统计结果 ===")
    print(f"原始出现次数: {total_occurrences}")
    print(f"不同的deviceId数量: {unique_device_ids}")
    print(f"去重后的组合数量: {unique_combinations_count}")
    
    # 分析deviceId重复情况
    device_id_counter = Counter(device_id_list)
    repeated_device_ids = {k: v for k, v in device_id_counter.items() if v > 1}
    
    if repeated_device_ids:
        print(f"\n重复的deviceId:")
        for device_id, count in list(repeated_device_ids.items())[:10]:
            print(f"  {device_id}: 出现 {count} 次")
        if len(repeated_device_ids) > 10:
            print(f"  ... 还有 {len(repeated_device_ids) - 10} 个重复的deviceId")
    else:
        print(f"\n所有deviceId都是唯一的")
    
    # 判断是否应该出现在结果中
    user_threshold = 50
    element_threshold = 50
    
    print(f"\n=== 阈值判断 ===")
    print(f"用户数阈值: {user_threshold}")
    print(f"实际用户数: {unique_combinations_count}")
    
    if unique_combinations_count >= user_threshold:
        print(f"✅ {target_device} 应该出现在高频保留结果中")
    elif '-' in target_device:
        print(f"❓ {target_device} 包含'-'，需要检查元素频次处理")
        
        # 分析元素
        elements = target_device.split('-')
        print(f"分隔元素: {elements}")
        
        # 这里需要完整的元素频次统计才能判断
        print(f"需要完整运行脚本才能确定元素频次是否>={element_threshold}")
    else:
        print(f"❌ {target_device} 用户数不足且不含'-'，不会出现在结果中")

def check_processing_log():
    """检查处理日志"""
    
    log_file = "device_name_cleaner.log"
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'SR1041D' in content:
            print(f"\n=== 日志中的SR1041D信息 ===")
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'SR1041D' in line:
                    print(f"行 {i+1}: {line}")
        else:
            print(f"\n日志中未找到SR1041D相关信息")
            
    except FileNotFoundError:
        print(f"\n未找到日志文件: {log_file}")
    except Exception as e:
        print(f"\n读取日志文件出错: {e}")

def main():
    """主函数"""
    
    file_path = "/home/<USER>/HBHGU20250818.csv"
    target_device = "SR1041D"
    
    # 调试设备名称
    debug_device_name(file_path, target_device)
    
    # 检查处理日志
    check_processing_log()
    
    print(f"\n=== 建议 ===")
    print("1. 如果用户数>=50，检查输出文件是否真的没有")
    print("2. 如果用户数<50，考虑降低user_threshold")
    print("3. 检查是否在数据清洗过程中被过滤")
    print("4. 确认输出文件的完整性")

if __name__ == "__main__":
    main()
